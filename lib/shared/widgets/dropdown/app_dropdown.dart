import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class AppDropdown<T> extends StatelessWidget {
  final List<DropdownMenuItem<T>> items;
  final T? selected;
  final ValueChanged<T?>? onChanged;
  final String? hintText;
  final List<Widget> Function(BuildContext)? selectedItemBuilder;
  final double? height;
  final double? menuMaxHeight;
  final Color? dropdownColor;
  const AppDropdown({
    super.key,
    required this.items,
    required this.selected,
    required this.onChanged,
    this.hintText,
    this.selectedItemBuilder,
    this.height,
    this.menuMaxHeight,
    this.dropdownColor,
  });

  @override
  Widget build(BuildContext context) {
    return InputDecorator(
      decoration: InputDecoration(
        floatingLabelBehavior: FloatingLabelBehavior.never,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5.gr),
          borderSide: BorderSide(color: context.theme.primaryColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5.gr),
          borderSide: BorderSide(color: Colors.transparent),
        ),
        fillColor: context.theme.inputDecorationTheme.fillColor,
        filled: true,
        isDense: true,
        hintStyle: context.textTheme.regular.fs13.copyWith(color: context.colorTheme.tabInactive),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5.gr),
          borderSide: BorderSide(color: context.theme.primaryColor.withAlpha(128)),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.0),
      ),
      child: DropdownButtonHideUnderline(
        child: SizedBox(
          height: height ?? 37.gh,
          child: DropdownButton(
            isDense: true,
            isExpanded: true,
            menuMaxHeight: menuMaxHeight,
            dropdownColor: dropdownColor ?? context.theme.cardColor,
            hint: Text(
              hintText ?? '',
              style: context.textTheme.regular.fs13.copyWith(color: context.colorTheme.tabActive),
            ),
            selectedItemBuilder: selectedItemBuilder,
            value: selected,
            items: items,
            icon: Padding(
              padding: const EdgeInsets.only(right: 5),
              child: Icon(Icons.keyboard_arrow_down_sharp, size: 15.gr, color: context.colorTheme.tabActive),
            ),
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }
}
