import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'dart:convert';

import 'package:gp_stock_app/generated/json/fund_pay_way.g.dart';

@JsonSerializable()
class FundPayWayListEntity {
	List<FundPayWay> list = [];

	FundPayWayListEntity();

	factory FundPayWayListEntity.fromJson(Map<String, dynamic> json) => $FundPayWayListEntityFromJson(json);

	Map<String, dynamic> toJson() => $FundPayWayListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class FundPayWay {
	String bankCode = '';
	String  bankName = '';
	String  icon = '';

	FundPayWay();

	factory FundPayWay.fromJson(Map<String, dynamic> json) => $FundPayWayFromJson(json);

	Map<String, dynamic> toJson() => $FundPayWayToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}