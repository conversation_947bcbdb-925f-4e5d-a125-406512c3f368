import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/fund_account/fund_pay_way.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

class FundAccountApi {


  /// 获取账号金额相关信息
  static Future<List<FundPayWay>> fetchSupportChannelList() async {
    final res = await Http().request<FundPayWayListEntity>(
      ApiEndpoints.supportFundChannel,
      method: HttpMethod.get,
    );
    if (res.isSuccess && res.data != null) {
      return res.data!.list;
    }
    return [];
  }
}