import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/user.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AuthApi {
  /// 获取是否需要网易行为验证
  static Future<(bool isSuccess, bool isNeedCaptcha)> fetchWangYiCaptchaRequired({required WangYiCaptchaType type}) async {
    // sceneType(1.Login登陆 2.SMS短信）
    final res = await Http().request(
      ApiEndpoints.wangYiCaptcha,
      method: HttpMethod.get,
      queryParameters: {"sceneType": type.code},
      needSignIn: false,
    );
    return (res.isSuccess, res.data == true);
  }

  /// 获取短信验证码
  static Future<bool> fetchOTP({
    required String phoneNumber,
    required String sendType,
    String? validate,
  }) async {
    final res = await Http().request<bool>(ApiEndpoints.sendMsg, needSignIn: false, params: {
      "mobile": phoneNumber,
      "sendType": sendType,
      if (validate != null) "validate": validate,
    });
    return res.data == true;
  }

  /// 登录
  static Future<UserModel?> login({
    required String mobile,
    required String password,
    required String smsCode,
    required LoginMode mode,
    String? validate,
  }) async {
    final res = await Http().request<UserModel>(ApiEndpoints.login,
        params: {
          "mobile": mobile,
          "password": password.toBase64(),
          "verifyType": mode.text,
          "smsCode": smsCode,
          if (validate != null) "validate": validate,
        },
        needSignIn: false);
    return res.data;
  }

  /// 获取用户数据
  static Future<UserModel?> getUserInfo() async {
    final res = await Http().request<UserModel>(ApiEndpoints.getUserInfo, method: HttpMethod.get);
    return res.data;
  }

  /// 注册
  static Future<bool> register({
    required String mobile,
    required String smsCode,
    required String password,
    String? inviteCode,
  }) async {
    final res = await Http().request(
      ApiEndpoints.register,
      needSignIn: false,
      params: {
        "mobile": mobile,
        "smsCode": smsCode,
        "password": password,
        if (inviteCode != null) "inviteCode": inviteCode,
      },
    );
    return res.isSuccess;
  }
}
