import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/im_url_service_impl.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/aes_encryption.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services/im_url_service.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import '../../shared/constants/enums.dart';
import '../api/network/network_helper.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class TencentIMUtils {
  static final timCoreInstance = TIMUIKitCore.getInstance();

  static Future<void> initTencent({
    required int sdkappid,
    required String userid,
    required String usersig,
    required Function() onLoginSuccess,
    required Function(int code, String error) onConnectFailed,
  }) async {
    IMUrlServiceManager.initialize(AppIMUrlService());
    final String? token = getIt<UserCubit>().state.token;
    if (token == null) {
      LogE("");
      return;
    }
    await timCoreInstance.init(
      sdkAppID: sdkappid,
      loglevel: LogLevelEnum.V2TIM_LOG_ALL,
      token: token,
      kDebug: kDebugMode,
      decrypt: (response) => AESEncryption().decryptResponse(response),
      listener: V2TimSDKListener(
        onConnectFailed: onConnectFailed,
        onConnectSuccess: onLoginSuccess,
        onConnecting: () {
          LogF("");
        },
        onKickedOffline: () {
          NetworkHelper.handleMessage(
            'kickedOfflineMessage'.tr(),
            type: HandleTypes.customDialog,
            snackBarType: SnackBarType.error,
            dialogKey: 'kicked_offline_dialog',
            onTap: () => getIt<NavigatorService>().popToRoot(),
          );
        },
        onSelfInfoUpdated: (V2TimUserFullInfo info) {
          LogI(info.toString());
        },
        onUserSigExpired: () {
          LogF("");
        },
      ),
    );
    final res = await timCoreInstance.login(userID: userid, userSig: usersig);
    // TODO: uncomment this line when you want to use push notification
    // TencentPushService().initialize();
    if (res.desc == "ok") {
      LogD("${res.toJson()}");
      onLoginSuccess();
    } else {
      LogE("${res.toJson()}");
    }
  }

  static Future<void> logoutTencent({
    required Function() onLogoutSuccess,
  }) async {
    final res = await timCoreInstance.logout();
    if (res.desc == "ok") {
      LogD("${res.toJson()}");
      onLogoutSuccess();
    } else {
      LogE("${res.toJson()}");
    }
  }
}
