import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:url_launcher/url_launcher.dart';

class SystemUtil {
  /// 使用系统浏览器访问url
  static void openUrlOnSystemBrowser({required String url, LaunchMode mode = LaunchMode.platformDefault,}) async {
    try {
      // 1. URL预处理和检查
      url = StringUtil.fixUrl(url);

      final uri = Uri.tryParse(url);
      if (uri == null) {
        GPEasyLoading.showToast("invalid_address".tr());
        return;
      }

      GPEasyLoading.showLoading();

      final canLaunch = await canLaunchUrl(uri);

      if (canLaunch) {
        await launchUrl(uri, mode: mode);
      } else {
        throw '$uri load failed';
      }

      GPEasyLoading.dismiss();
    } catch (e) {
      GPEasyLoading.showToast("${'open_browser_failed'.tr()} $e");
      GPEasyLoading.dismiss();
      rethrow; // 使用rethrow而不是throw Exception(e)以保留原始堆栈跟踪
    }
  }
}
