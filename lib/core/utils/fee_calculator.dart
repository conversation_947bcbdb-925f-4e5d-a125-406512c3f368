// 费用计算场景枚举
import 'package:decimal/decimal.dart';
import 'package:gp_stock_app/core/models/entities/trade/trade_handling_fee_config_entity.dart';

class FeeCalculator {
  static double calculateTradeHandlingFee({
    required num tradeAmount,
    required List<TradeHandlingFeeConfigEntity> chargeList,
    num tradeNum = 0,
    bool isStockIndex = false,
  }) {
    double totalFee = 0;
    for (var charge in chargeList) {
      Decimal fee0 = Decimal.zero;
      var tradeAmountDecimal = Decimal.parse(tradeAmount.toString());
      final calculateValueDecimal = Decimal.parse(charge.calculateValue.toString());

      /// 股指需 交易金额 x 杠杆倍数
      if (isStockIndex) {
        tradeAmountDecimal = tradeAmountDecimal * Decimal.parse(tradeNum.toString());
      }
      switch (charge.calculateType) {
        /// 1: 以金额计算(tradeAmount * 费率)
        case 1:
          fee0 = tradeAmountDecimal * (calculateValueDecimal / Decimal.fromInt(100)).toDecimal();
          fee0 = fee0.round(scale: 2);
          break;

        /// 2.以数量计算 (交易手数*计算值)
        case 2:
          fee0 = calculateValueDecimal * Decimal.parse(tradeNum.toString());
          break;

        /// 3.固定收费（不进行四舍五入）
        case 3:
          fee0 = calculateValueDecimal;
          break;
      }


      if (charge.calculateType != 3) {
        fee0 = fee0.clamp(
          Decimal.parse(charge.min.toString()),
          Decimal.parse(charge.max.toString()),
        );
      }

      totalFee += _applyRounding(fee0, charge);
    }

    return totalFee;
  }

  static double _applyRounding(Decimal value, TradeHandlingFeeConfigEntity charge) {
    Function? roundFn;
    switch (charge.roundType) {
      case 1:
        roundFn = (Decimal v, int p) => v.ceil(scale: p).toDouble();
        break;
      case 2:
        roundFn = (Decimal v, int p) => v.round(scale: p).toDouble();
        break;
      case 3:
        roundFn = (Decimal v, int p) => v.truncate(scale: p).toDouble();
        break;
    }
    double roundedValue = roundFn?.call(value, charge.roundPrecision) ?? value.toDouble();
    return clampDouble(roundedValue, charge.min, charge.max);
  }
}

// Dart SDK 没有 clampDouble，手动实现
T clampDouble<T extends num>(T value, T min, T max) => value < min ? min : (value > max ? max : value);
