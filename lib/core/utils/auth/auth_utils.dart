import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/secure_storage_helper.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/alert_dilaog/auth_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../shared/constants/keys.dart';

class AuthUtils {
  AuthUtils._() {
    checkIfFirstInstall();
  }
  static AuthUtils? _instance;
  static final AuthUtils instance = (_instance ??= AuthUtils._());

  /// 检查是否首次安装，需清空之前存储的数据
  void checkIfFirstInstall() async {
    final prefs = await SharedPreferences.getInstance();
    final isFirstInstall = prefs.getBool('secure_initialized') ?? false;
    if (isFirstInstall) {
      await SecureStorageHelper().deleteAllSecureData(); // 清空旧的 keychain 数据
      await prefs.setBool('secure_initialized', true); // 标记初始化完成
    }
  }

  static void verifyAuth(VoidCallback onTap) async {
    final isSignedIn = getIt<UserCubit>().isLoggedIn;
    if (isSignedIn) {
      onTap();
    } else {
      final result = await showDialog<bool>(
            context: getIt<NavigatorService>().navigatorKey.currentContext!,
            builder: (dialogContext) => AuthRequiredDialog(),
          ) ??
          false;
      if (result) {
        Future.delayed(const Duration(milliseconds: 300), () => onTap());
      }
    }
  }

  Future<void> rememberPassword(bool value, {String? username, String? password}) async {
    await SecureStorageHelper().writeSecureData(LocalStorageKeys.isRememberPassword, value.toString());
    await SecureStorageHelper().writeSecureData(LocalStorageKeys.username, username ?? '');
    await SecureStorageHelper().writeSecureData(LocalStorageKeys.password, password ?? '');
  }

  Future<(bool, String?, String?)> get isRememberPassword async {
    final String? isRememberPassword = await SecureStorageHelper().readSecureData(LocalStorageKeys.isRememberPassword);
    final String? username = await SecureStorageHelper().readSecureData(LocalStorageKeys.username);
    final String? password = await SecureStorageHelper().readSecureData(LocalStorageKeys.password);
    return (isRememberPassword == 'true', username, password);
  }

  Future<void> clearRememberedCredentials() async {
    await SecureStorageHelper().deleteSecureData(LocalStorageKeys.isRememberPassword);
    await SecureStorageHelper().deleteSecureData(LocalStorageKeys.username);
    await SecureStorageHelper().deleteSecureData(LocalStorageKeys.password);
  }
}
