import 'package:gp_stock_app/features/contract/domain/models/date_filter.dart';
import 'package:gp_stock_app/features/contract/domain/models/transaction_type.dart';

class FilterConstants {
  static const List<DateFilter> filters = [
    DateFilter(label: 'Today', days: 0),
    DateFilter(label: 'Yesterday', days: 1),
    DateFilter(label: 'Last 3 Days', days: 3),
    DateFilter(label: 'Last 7 Days', days: 7),
    DateFilter(label: 'Last 15 Days', days: 15),
    DateFilter(label: 'This Month', days: 0, isMonth: true),
    DateFilter(label: 'Last Month', days: 30),
  ];

  static const List<TransactionType> contractTypes = [
    TransactionType(type: 0, label: 'all'),
    TransactionType(type: 1, label: 'deposit'),
    TransactionType(type: 2, label: 'withdrawal'),
    TransactionType(type: 3, label: 'transfer_to_contract'),
    TransactionType(type: 4, label: 'transfer_from_contract'),
    TransactionType(type: 5, label: 'create_contract'),
    TransactionType(type: 6, label: 'settle_contract'),
    TransactionType(type: 7, label: 'buy_transaction'),
    TransactionType(type: 8, label: 'sell_transaction'),
    TransactionType(type: 9, label: 'admin_balance_adjustment'),
    TransactionType(type: 10, label: 'dividend_payment'),
    TransactionType(type: 11, label: 'interest_deduction'),
    TransactionType(type: 12, label: 'trading_fee'),
    TransactionType(type: 13, label: 'activity_reward'),
    TransactionType(type: 14, label: 'signin_bonus'),
    TransactionType(type: 15, label: 'contract_unfreeze'),
    TransactionType(type: 16, label: 'applyContractInterestRebate'),
    TransactionType(type: 17, label: 'expandContractInterestRebate'),
    TransactionType(type: 18, label: 'deposit_bonus'),
    TransactionType(type: 19, label: 'entrustRevoke'),
    TransactionType(type: 20, label: 'missionAward'),
    TransactionType(type: 21, label: 'unfreezeOrderAmountSimple'),
    TransactionType(type: 22, label: 'unfreezeOrderFeeSimple'),
    TransactionType(type: 23, label: 'freezeOrderBuyAmount'),
    TransactionType(type: 24, label: 'freezeOrderBuyFee'),
    TransactionType(type: 25, label: 'add_contract_cash_amount'),
    TransactionType(type: 26, label: 'admin_adjust_reduce'),
    TransactionType(type: 27, label: 'contractRenewalInterestRebate'),
    TransactionType(type: 28, label: 'stockPurchaseFeeRebate'),
    TransactionType(type: 29, label: 'sellStockCommissionRebate'),
    TransactionType(type: 30, label: 'buyIndexCommissionRebate'),
    TransactionType(type: 31, label: 'sellIndexCommissionRebate'),
    TransactionType(type: 32, label: 'add_futures_margin'),
    TransactionType(type: 33, label: 'futures_buy_fee_rebate'),
    TransactionType(type: 34, label: 'futures_sell_fee_rebate'),
  ];
}
