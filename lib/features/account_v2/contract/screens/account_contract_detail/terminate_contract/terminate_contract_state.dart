import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class TerminateContractState extends Equatable {
  final ContractSummaryPageRecord model;
  final DataStatus netStatus;

  const TerminateContractState({
    required this.model,
    this.netStatus = DataStatus.idle,
  });

  TerminateContractState copyWith({
    ContractSummaryPageRecord? model,
    DataStatus? netStatus,
  }) {
    return TerminateContractState(
      model: model ?? this.model,
      netStatus: netStatus ?? this.netStatus,
    );
  }

  @override
  List<Object?> get props => [model, netStatus];
}
