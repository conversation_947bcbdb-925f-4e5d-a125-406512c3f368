import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/account/domain/models/user_banks/user_bank_model.dart';
import 'package:gp_stock_app/features/account/domain/services/bank_service.dart';
import 'package:gp_stock_app/features/account/logic/deposit/deposit_state.dart';
import 'package:gp_stock_app/shared/app/extension/string_extension.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import '../../domain/models/deposit_records/deposit_record.dart';

class DepositCubit extends Cubit<DepositState> {
  DepositCubit() : super(DepositState());

  final _bankRepository = BankService();
  final int _pageSize = 20;

  void pay({
    required String payAmount,
    required int userBankId,
    required String orderNumber,
    required int sysBankCardId,
  }) async {
    if (state.depositStatus.isLoading) return;
    emit(state.copyWith(depositStatus: DataStatus.loading));
    try {
      int amount = int.parse(payAmount);
      final result = await _bankRepository.pay(
        payAmount: amount,
        userBankId: userBankId,
        orderNumber: orderNumber,
        sysBankCardId: sysBankCardId,
      );
      if (result.isSuccess) {
        emit(state.copyWith(depositStatus: DataStatus.success));
      } else {
        emit(state.copyWith(error: result.error, depositStatus: DataStatus.failed));
      }
    } on Exception catch (e) {
      emit(state.copyWith(error: e.toString(), depositStatus: DataStatus.failed));
    }
  }

  void updateSelectedBank(UserBankModel? bank) {
    emit(state.copyWith(selectedBank: bank));
    validateForm();
  }

  void updateDepositAmount(String? amount) {
    emit(state.copyWith(depositAmount: amount));
    validateForm();
  }

  void updateOrderNumber(String? orderNumber) {
    emit(state.copyWith(orderNumber: orderNumber));
    validateForm();
  }

  void validateForm() {
    final isValid =
        state.depositAmount.isNotNullNorEmpty && state.selectedBank != null && state.orderNumber.isNotNullNorEmpty;
    emit(state.copyWith(isFormValid: isValid));
  }

  void clearForm() {
    emit(state.copyWith(depositAmount: '', selectedBank: null));
  }

  Future<void> getDepositRecords({bool isLoadMore = false}) async {
    if (state.depositRecordsFetchStatus == DataStatus.loading) return;

    final int pageNumber = isLoadMore ? (state.depositCurrent ?? 0) + 1 : 1;

    if (!isLoadMore) {
      emit(state.copyWith(depositRecordsFetchStatus: DataStatus.loading));
    }

    try {
      final result = await _bankRepository.getDepositRecords(pageNumber, _pageSize);

      if (result.isSuccess && result.data != null) {
        final List<DepositRecord> parsedRecords = result.data!.records ?? [];

        final List<DepositRecord> currentList =
            isLoadMore ? (List<DepositRecord>.from(state.depositRecords ?? [])..addAll(parsedRecords)) : parsedRecords;

        emit(state.copyWith(
          depositRecordsFetchStatus: DataStatus.success,
          depositRecords: currentList,
          depositCurrent: result.data?.current,
          depositTotal: result.data?.total,
        ));
      } else {
        emit(state.copyWith(
          depositRecordsFetchStatus: DataStatus.failed,
          error: result.error ?? 'failedToLoad'.tr(), // Using translation key
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        depositRecordsFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }
}
