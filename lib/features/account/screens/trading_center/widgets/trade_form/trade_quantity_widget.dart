import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/shared/widgets/counter_textfield.dart';

class TradeQuantityWidget extends StatefulWidget {
  /// A widget that handles trade quantity input with increment/decrement controls.
  ///
  /// This widget provides a counter-style text field that allows users to:
  /// - Manually input trade quantities
  /// - Increment/decrement quantities using buttons
  /// - Automatically rounds input to valid lot sizes
  /// - Enforces quantity limits based on available quantity
  /// - Handles different decimal precision for index vs non-index trading
  ///
  /// The widget integrates with [TradingCubit] for state management and
  /// automatically formats quantities based on the trading type (index or regular).
  const TradeQuantityWidget({super.key});

  @override
  State<TradeQuantityWidget> createState() => _TradeQuantityWidgetState();
}

class _TradeQuantityWidgetState extends State<TradeQuantityWidget> {
  /// Controller for managing the quantity input text field
  final TextEditingController quantityController = TextEditingController();

  /// Focus node to track the input field's focus state
  final FocusNode focusNode = FocusNode();

  /// Rounds the input value to the nearest valid lot size and ensures it's within limits
  ///
  /// [currentValue] The raw input value to be processed
  /// [lotSize] The minimum trading lot size
  /// [availableQuantity] The maximum allowed quantity
  /// returns The rounded and bounded quantity value
  double _roundToLotSize(double currentValue, double lotSize, double availableQuantity) {
    double roundedValue = ((currentValue + (lotSize / 2)) ~/ lotSize) * lotSize;
    roundedValue = roundedValue == 0 ? lotSize : roundedValue;
    return roundedValue > availableQuantity ? availableQuantity : roundedValue;
  }

  /// Updates the quantity display format based on trading type
  ///
  /// value The quantity value to format
  /// isIndexTrading Whether index trading mode is active
  /// returns The formatted quantity string
  String _formatQuantity(double value, bool isIndexTrading) {
    return value.toStringAsFixed(isIndexTrading ? 1 : 0);
  }

  @override
  void dispose() {
    quantityController.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState,
        ({double quantity, double lotSize, bool isIndexTrading, double availableQuantity})>(
      selector: (state) => (
        quantity: state.quantity,
        lotSize: state.lotSize,
        isIndexTrading: state.isIndexTrading,
        availableQuantity: state.availableQuantity,
      ),
      builder: (context, state) {
        final tradingCubit = context.read<TradingCubit>();

        /// 是否可增加
        final canIncrement = tradingCubit.canIncrementQuantity();

        /// 是否可减少
        final canDecrement = tradingCubit.canDecrementQuality();
        final formattedQuantity = _formatQuantity(state.quantity, state.isIndexTrading);
        if (quantityController.text != formattedQuantity && !focusNode.hasFocus) {
          quantityController.text = formattedQuantity;
          LogI(">>>formattedQuantity: $formattedQuantity");
        }
        return Expanded(
          child: CounterTextfield(
            onDecrementPressed: canDecrement ? () => context.read<TradingCubit>().decrementQuantity() : null,
            onIncrementPressed: canIncrement ? () => context.read<TradingCubit>().incrementQuantity() : null,
            controller: quantityController,
            focusNode: focusNode,
            onFocusChanged: () {
              final currentValue = double.tryParse(quantityController.text) ?? state.lotSize;
              final roundedValue = _roundToLotSize(currentValue, state.lotSize, state.availableQuantity);
              quantityController.text = _formatQuantity(roundedValue, state.isIndexTrading);
              tradingCubit.setQuantity(roundedValue);
            },
          ),
        );
      },
    );
  }
}
