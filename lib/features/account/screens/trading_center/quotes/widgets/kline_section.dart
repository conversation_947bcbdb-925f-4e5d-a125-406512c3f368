import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/kline_selector.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/tick_list_section.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/time_zone.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/web_socket_actions.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/mixin/web_socket_mixin.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/models/web_scoket_message.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:k_chart_plus/k_chart_plus.dart';

class KlineSection extends StatefulWidget {
  const KlineSection({super.key, required this.instrument, this.showTicks = false});

  final Instrument instrument;
  final bool showTicks;

  @override
  State<KlineSection> createState() => _KlineSectionState();
}

class _KlineSectionState extends State<KlineSection> with WebSocketMixin {
  StreamSubscription<WebSocketMessage>? _marketSubscription;
  List<KLineEntity> _processData(List<KlineItem>? list, bool isLine) {
    if (list == null || list.isEmpty) return [];

    List<KLineEntity> klineEntities = [];
    double? previousClose;
    int offset = countryTimeOffsets['CN'] ?? 0;

    for (int i = 0; i < list.length; i++) {
      final item = list[i];

      // Skip invalid items
      if (item.time == null || item.time! <= 0) continue;

      // Skip empty/invalid candlestick data (when API returns data:{})
      // But allow real-time data that has price but no OHLC
      if (!isLine &&
          ((item.open ?? 0) == 0 &&
              (item.close ?? 0) == 0 &&
              (item.high ?? 0) == 0 &&
              (item.low ?? 0) == 0 &&
              (item.volume ?? 0) == 0 &&
              (item.price ?? 0) == 0)) {
        continue;
      }

      // For line charts, skip if price is null or zero
      if (isLine && (item.price == null || item.price! <= 0)) {
        continue;
      }

      double openPrice;
      double closePrice;
      double highPrice;
      double lowPrice;

      if (isLine) {
        openPrice = item.price ?? 0;
        closePrice = item.price ?? 0;
        highPrice = item.price ?? 0;
        lowPrice = item.price ?? 0;
      } else {
        // For candlestick data
        if (item.open != null && item.open! > 0) {
          // Historical data with proper OHLC
          openPrice = item.open!;
          closePrice = item.close ?? item.price ?? 0;
          highPrice = item.high ?? item.price ?? 0;
          lowPrice = item.low ?? item.price ?? 0;
        } else if (item.price != null && item.price! > 0) {
          // Real-time data with only price - use previous close as open
          openPrice = previousClose ?? item.price!;
          closePrice = item.price!;
          highPrice = item.price!;
          lowPrice = item.price!;
        } else {
          // Fallback
          openPrice = previousClose ?? item.close ?? 0;
          closePrice = item.close ?? 0;
          highPrice = item.high ?? item.close ?? 0;
          lowPrice = item.low ?? item.close ?? 0;
        }
      }

      final adjustedTime = DateTime.fromMillisecondsSinceEpoch(
        item.time! * 1000,
        isUtc: true,
      ).add(Duration(hours: offset)).millisecondsSinceEpoch;

      final klineEntity = KLineEntity.fromCustom(
        time: adjustedTime,
        close: closePrice,
        open: openPrice,
        high: highPrice,
        low: lowPrice,
        vol: item.volume ?? 0,
        amount: item.price ?? 0,
      );

      klineEntities.add(klineEntity);
      previousClose = closePrice;
    }

    if (klineEntities.isNotEmpty) {
      try {
        DataUtil.calculate(klineEntities, [5, 10, 20, 30, 60]);
      } catch (e) {
        // handle calculation errors
      }
    }

    return klineEntities;
  }

  void _listenToSocketUpdates() {
    // Listen for market data updates
    _marketSubscription = onMessage(SocketEvents.market).listen(_handleMarketUpdate);

    // Subscribe to timeline data for this instrument
    webSocketService.send({
      'type': SocketEvents.market,
      'action': SocketActions.timeLine,
      'params': {
        'instrument': widget.instrument.instrument,
        'period': 'day',
        'operate': 'subscribe',
      }
    });
  }

  void _handleMarketUpdate(WebSocketMessage message) {
    // Validate response code
    if (message.data['code'] != 200) return;

    // Parse and validate message data
    final stockMarketUpdate = StockKlineResponse.fromJson(message.data);
    if (stockMarketUpdate.data == null) return;

    // Check if this update is for our instrument
    final isMatchingInstrument = stockMarketUpdate.data?.detail?.instrument == widget.instrument.instrument;
    if (!isMatchingInstrument) return;

    // Check if market is open
    final isMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(stockMarketUpdate.data?.detail?.market);
    if (!isMarketOpen) return;

    final tradingCubit = context.read<TradingCubit>();
    final currentKlineData = tradingCubit.state.klineDetailList;

    if (currentKlineData != null && currentKlineData.data != null && stockMarketUpdate.data?.list != null) {
      final newKlineItems = stockMarketUpdate.data!.list!;
      final existingList = currentKlineData.data!.list ?? [];
      List<KlineItem> updatedList = [];

      // Handle real-time price updates (single item with price)
      if (newKlineItems.length == 1 && newKlineItems.first.price != null) {
        // Add existing items, replacing the last one if it has the same timestamp
        for (int i = 0; i < existingList.length; i++) {
          final existingItem = existingList[i];
          final isLastItem = i == existingList.length - 1;
          final hasSameTimeAsNew = newKlineItems.any((newItem) => newItem.time == existingItem.time);

          if (isLastItem && hasSameTimeAsNew) {
            continue; // Skip last item to replace with new data
          }
          updatedList.add(existingItem);
        }
        updatedList.addAll(newKlineItems);
      } else {
        // Handle regular historical data updates
        for (final existingItem in existingList) {
          final hasConflict = newKlineItems.any((newItem) => newItem.time == existingItem.time);
          if (!hasConflict) {
            updatedList.add(existingItem);
          }
        }
        updatedList.addAll(newKlineItems);
      }

      // Sort by time to maintain chronological order
      updatedList.sort((a, b) => (a.time ?? 0).compareTo(b.time ?? 0));

      // Update the kline data
      final updatedKlineData = currentKlineData.copyWith(
        data: currentKlineData.data!.copyWith(
          list: updatedList,
          detail: stockMarketUpdate.data?.detail,
        ),
      );

      if (mounted) {
        tradingCubit.updateKlineDetailList(updatedKlineData);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<TradingCubit>().getKlineDetailList(widget.instrument.instrument, KlineConstants.options[0]);
    _listenToSocketUpdates();
  }

  @override
  void dispose() {
    _marketSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.theme.cardColor,
      child: Column(
        children: [
          KlineSelector(instrument: widget.instrument.instrument),
          BlocBuilder<TradingCubit, TradingState>(
            builder: (context, state) {
              if (state.klineDetailListStatus == DataStatus.loading) {
                return ShimmerWidget(height: 180.gh);
              }

              if (state.klineDetailListStatus == DataStatus.failed) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, color: Colors.red),
                      SizedBox(height: 8),
                      Text("chart_failed_to_load".tr(), style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                );
              }

              final isLine = state.klineOption?.type == "timeLine";
              final showTicks = state.klineOption?.id == "intraday" && widget.showTicks;
              final scaleX = switch (state.klineOption?.id) {
                "weekly-kline" => 0.5,
                "monthly-kline" => 0.5,
                "yearly-kline" => 1.0,
                "intraday" => 0.15,
                "5day" => 0.03,
                _ => 0.8,
              };

              // Process data and render chart
              final chartData = _processData(state.klineDetailList?.data?.list, isLine);
              final closePrice = state.klineDetailList?.data?.detail?.close;
              final stockGainPercent = state.stockInfo?.gain;

              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(8, 0, 8, 4),
                      child: chartData.isEmpty
                          ? SizedBox(
                              height: 180.gh,
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.timeline, color: Colors.grey.withValues(alpha: 0.5), size: 32),
                                    SizedBox(height: 8),
                                    Text(
                                      "chart_waiting_for_data".tr(),
                                      style: TextStyle(fontSize: 12, color: Colors.grey),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
                              child: KChartWidget(
                                chartData,
                                ChartStyle(),
                                ChartColors(
                                  upColor: const Color(0xFFD2544F),
                                  dnColor: const Color(0xFF5DAF78),
                                  gridColor: Colors.transparent,
                                  bgColor: context.theme.cardColor,
                                  ma5Color: const Color(0xffE5B767),
                                  ma10Color: const Color(0xff1FD1AC),
                                  ma30Color: const Color(0xffB48CE3),
                                  ma60Color: const Color(0xFFD5405D),
                                ),
                                getColorCallback: (value) => value.getValueColor(context),
                                mBaseHeight: 0.27.gsh,
                                isTrendLine: false,
                                scaleX: scaleX,
                                mainState: MainState.MA,
                                volHidden: false,
                                isTapShowInfoDialog: true,
                                secondaryStateLi: {},
                                timeFormat: TimeFormat.YEAR_MONTH_DAY_WITH_HOUR,
                                verticalTextAlignment: VerticalTextAlignment.right,
                                isLine: isLine,
                                xFrontPadding: 0,
                                closePrice: closePrice,
                                showDate: !showTicks,
                                locale: context.locale.languageCode,
                                gainPercent: stockGainPercent,
                                isMarketOpen: getIt<MarketStatusCubit>()
                                    .isMarketOpen(state.klineDetailList?.data?.detail?.market),
                                maDayList: const [5, 10, 20, 30, 60],
                              ),
                            ),
                    ),
                  ),
                  if (showTicks)
                    TickListSection(
                        instrument: widget.instrument, getColorCallback: (value) => value.getValueColor(context)),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}

class EmptyChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.1)
      ..strokeWidth = 1;

    // Draw grid lines
    final gridSpacing = size.height / 5;
    for (int i = 1; i < 5; i++) {
      final y = gridSpacing * i;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw vertical lines
    final verticalSpacing = size.width / 6;
    for (int i = 1; i < 6; i++) {
      final x = verticalSpacing * i;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
