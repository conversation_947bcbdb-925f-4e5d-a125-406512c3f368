import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_future_sltp.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_orders.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_dialog_add_margin.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/f_trade_acct_entrust_list.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/f_trade_acct_position_list.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/logic/f_trade_acct_entrust_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/logic/f_trade_acct_position_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/market/widgets/market_table_header.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class FTradeAcctScreen extends StatefulWidget {
  final CNFuturesMarketType type;
  final FTradeListItemModel data;
  final void Function(int) onChangeAllInfoScreenTitlesAction;

  const FTradeAcctScreen({
    super.key,
    required this.type,
    required this.data,
    required this.onChangeAllInfoScreenTitlesAction,
  });

  @override
  State<FTradeAcctScreen> createState() => _FTradeAcctScreenState();
}

class _FTradeAcctScreenState extends State<FTradeAcctScreen> {
  int currentPageIdx = 1;
  final RefreshController _poistionRefreshCtrl = RefreshController(initialRefresh: false);
  final RefreshController _entrustRefreshCtrl = RefreshController(initialRefresh: false);
  late String instrument;

  @override
  void initState() {
    super.initState();
    instrument = widget.data.makeInstrument();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FTradeAcctPositionCubit>().fetchData(instrument: instrument);
      context.read<FTradeAcctPositionCubit>().startPolling();
      context.read<FTradeAcctEntrustCubit>().fetchData(instrument: instrument);
      context.read<FTradeAcctEntrustCubit>().startPolling();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FTradeAcctPositionCubit, FTradeAcctPositionState>(
      builder: (context, state) {
        return BlocBuilder<FTradeAcctEntrustCubit, FTradeAcctEntrustState>(
          builder: (context, state) {
            return Column(
              children: [
                AnimationLimiter(
                  child: _buildTitles(context),
                ),
                8.verticalSpace,
                currentPageIdx == 0 ? _buildEntrustList() : _buildPositionList()
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildEntrustList() {
    return BlocBuilder<FTradeAcctEntrustCubit, FTradeAcctEntrustState>(builder: (context, state) {
      return FTradeAcctEntrustList(
        dataStatus: state.dataStatus,
        orderModel: state.orderModel,
        refreshCtrl: _entrustRefreshCtrl,
        onUserActions: onEntrustUserActions,
      );
    });
  }

  void onEntrustUserActions(String actionType, FTradeAcctOrderRecords record) {
    if (actionType == 'tap') {
      context.read<TradingCubit>().getKlineDetailList(record.makeInstrument(), KlineConstants.options[0]);
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (_) {
          return MultiBlocProvider(
            providers: [
              BlocProvider.value(
                value: context.read<TradingCubit>(),
              ),
              BlocProvider(
                create: (_) => FTradeKLineCubit(FTradeKLineScrollRepository()),
              ),
            ],
            child: Builder(builder: (innerContext) {
              if (record.isCnFTrade) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  innerContext
                      .read<FTradeKLineCubit>()
                      .fetchTimeLineSubData(instrument: record.instrument, period: 'day');
                });
              }
              return TradeBottomsheetOrders(data: record.toOrderRecord(), isTradeDetails: false, isTrading: true);
            }),
          );
        },
      );
    }
    if (actionType == 'cancle') {
      showDialog(
        context: context,
        builder: (_) => BlocProvider.value(
          value: context.read<TradingCubit>(),
          child: CancelOrderDialog(orderId: record.id),
        ),
      );
    }
  }

  Widget _buildPositionList() {
    return BlocBuilder<FTradeAcctPositionCubit, FTradeAcctPositionState>(builder: (context, state) {
      return FTradeAcctPositionList(
        dataStatus: state.dataStatus,
        orderModel: state.orderModel,
        refreshCtrl: _poistionRefreshCtrl,
        onUserActions: (actionType, data) => onPositionUserActions(context, actionType, data),
      );
    });
  }

  void onPositionUserActions(BuildContext context, String actionType, FTradeAcctOrderRecords record) async {
    if (!context.mounted) return;
    final tradeType = TradeTypeOption.fromValue(record.tradeType);
    final tradeColor = tradeType.color(context);

    if (actionType == 'tap') {
      widget.onChangeAllInfoScreenTitlesAction(1);
    }
    if (actionType == 'tpsl' && mounted) {
      // 止盈止损
      TradeBottomSheetFutureSlTp(
          context: context,
          contractName: record.symbolName,
          tradeType: tradeType,
          directionText: tradeType.shortText,
          directionColor: tradeColor,
          openPrice: record.buyAvgPrice,
          currentPrice: record.stockPrice,
          currency: record.currency,
          restNum: record.restNum,
          onPressedOk: (takeProfitValue, stopLossValue) async {
            if (tradeType == TradeTypeOption.openLong) {
              if (takeProfitValue != 0 && takeProfitValue <= record.buyAvgPrice) {
                GPEasyLoading.showToast("take_profit_above_entry".tr()); // 止盈价格必须高于买入价格
              } else if (stopLossValue != 0 && stopLossValue >= record.buyAvgPrice) {
                GPEasyLoading.showToast("stop_loss_below_entry".tr()); // 止损价格必须低于买入价格
              }
            } else {
              if (takeProfitValue != 0 && takeProfitValue >= record.buyAvgPrice) {
                GPEasyLoading.showToast("take_profit_below_entry".tr()); // 止盈价格必须低于开仓价格
              } else if (stopLossValue != 0 && stopLossValue <= record.buyAvgPrice) {
                GPEasyLoading.showToast("stop_loss_above_entry".tr()); // 止损价格必须高于开仓价格
              }
            }

            final flag = await getIt<AccountScreenCubitV2>().setFuturesStopLine(
                positionId: record.id, takeProfitValue: takeProfitValue, stopLossValue: stopLossValue);
            if (flag) {
              GPEasyLoading.showToast("success".tr());
              await Future.delayed(const Duration(milliseconds: 500));
              if (context.mounted) Navigator.of(context).pop();
            }
          }).show();
    }
    if (actionType == 'add') {
      // 追加
      await TradeAddMarginDialog(
        context,
        data: record,
        availableMarginStream: context.read<FTradeAcctPositionCubit>().stream.map((state) {
          // 从FTradeAcctPositionCubit的状态中找到对应的订单记录
          final updatedRecord = state.orderModel?.records.where((r) => r.id == record.id).firstOrNull;

          return updatedRecord?.availableMargin ?? record.availableMargin;
        }).distinct(), // 避免重复的值
        onPressedSubmit: (amount) async {
          final flag = await getIt<AccountScreenCubitV2>().addMargin(positionId: record.id, amount: amount);
          if (flag) {
            GPEasyLoading.showToast("success".tr());
            await Future.delayed(const Duration(milliseconds: 500));
            if (context.mounted) Navigator.of(context).pop();
          }
        },
      ).show();
    }
    if (actionType == 'tapDetail' && context.mounted) {
      // 详情
      getIt<NavigatorService>().push(AppRouter.routeSpotPositionDetail, arguments: {
        'id': record.id,
      });
    }
  }

  Widget _buildTitles(BuildContext context) {
    return BlocBuilder<FTradeAcctPositionCubit, FTradeAcctPositionState>(
      builder: (context, positionState) {
        return BlocBuilder<FTradeAcctEntrustCubit, FTradeAcctEntrustState>(builder: (context, entrustState) {
          final entrustCount = entrustState.orderModel?.records.length ?? 0;
          final String entrustCountStr;
          if (entrustCount == 0) {
            entrustCountStr = '';
          } else {
            entrustCountStr = '($entrustCount)';
          }
          final positionCount = positionState.orderModel?.records.length ?? 0;
          final String positionCountStr;
          if (positionCount == 0) {
            positionCountStr = '';
          } else {
            positionCountStr = '($positionCount)';
          }

          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 300),
              childAnimationBuilder: (widget) => SlideAnimation(
                horizontalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: [
                SizedBox(
                  width: 100.gw,
                  child: MarketTableHeader(
                    title: '${'accountMarketTableTitle3'.tr()}$entrustCountStr',
                    isSelected: currentPageIdx == 0,
                    onTap: () {
                      setState(() {
                        currentPageIdx = 0;
                      });
                    },
                  ),
                ),
                15.horizontalSpace,
                SizedBox(
                  width: 100.gw,
                  child: MarketTableHeader(
                    title: '${'accountMarketTableTitle1'.tr()}$positionCountStr',
                    isSelected: currentPageIdx == 1,
                    onTap: () {
                      setState(() {
                        currentPageIdx = 1;
                      });
                    },
                  ),
                ),
              ],
            ),
          );
        });
      },
    );
  }
}
