import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/fund_account/fund_pay_way.dart';

FundPayWayListEntity $FundPayWayListEntityFromJson(Map<String, dynamic> json) {
  final FundPayWayListEntity fundPayWayListEntity = FundPayWayListEntity();
  final List<FundPayWay>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<FundPayWay>(e) as FundPayWay).toList();
  if (list != null) {
    fundPayWayListEntity.list = list;
  }
  return fundPayWayListEntity;
}

Map<String, dynamic> $FundPayWayListEntityToJson(FundPayWayListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension FundPayWayListEntityExtension on FundPayWayListEntity {
  FundPayWayListEntity copyWith({
    List<FundPayWay>? list,
  }) {
    return FundPayWayListEntity()
      ..list = list ?? this.list;
  }
}

FundPayWay $FundPayWayFromJson(Map<String, dynamic> json) {
  final FundPayWay fundPayWay = FundPayWay();
  final String? bankCode = jsonConvert.convert<String>(json['bankCode']);
  if (bankCode != null) {
    fundPayWay.bankCode = bankCode;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    fundPayWay.bankName = bankName;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    fundPayWay.icon = icon;
  }
  return fundPayWay;
}

Map<String, dynamic> $FundPayWayToJson(FundPayWay entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['bankCode'] = entity.bankCode;
  data['bankName'] = entity.bankName;
  data['icon'] = entity.icon;
  return data;
}

extension FundPayWayExtension on FundPayWay {
  FundPayWay copyWith({
    String? bankCode,
    String? bankName,
    String? icon,
  }) {
    return FundPayWay()
      ..bankCode = bankCode ?? this.bankCode
      ..bankName = bankName ?? this.bankName
      ..icon = icon ?? this.icon;
  }
}